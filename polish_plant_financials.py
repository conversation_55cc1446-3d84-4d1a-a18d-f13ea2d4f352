#!/usr/bin/env python3
"""
Polish Power Plant Financial Information Extractor
Identifies legal entities and retrieves financial information for coal-fired power plants in Poland.
Focuses on SPVs, KRS registry data, and financial statements from Polish sources.
"""

import os
import time
import pandas as pd
import json
import re
from datetime import datetime
from gemini_research_agent import GeminiResearchAgent
from dotenv import load_dotenv

load_dotenv()

class PolishPlantFinancialsExtractor:
    """Extract financial information for Polish power plants using Gemini Research Agent."""
    
    def __init__(self):
        self.research_agent = GeminiResearchAgent()
        
    def extract_plant_financials(self, plant_name: str) -> dict:
        """
        Extract financial and legal entity information for a Polish power plant.
        
        Args:
            plant_name: Name of the Polish power plant to research
            
        Returns:
            Dictionary containing legal entity and financial information
        """
        
        print(f"🔍 Processing Polish Plant: {plant_name}")
        
        try:
            # Step 1: Legal entity identification research
            legal_research = self._conduct_legal_entity_research(plant_name)
            
            # Step 2: Financial information research
            financial_research = self._conduct_financial_research(plant_name, legal_research)
            
            # Step 3: Extract structured data
            structured_data = self._extract_structured_data(plant_name, legal_research, financial_research)
            
            return structured_data
            
        except Exception as e:
            print(f"❌ Error processing {plant_name}: {e}")
            return {
                "plant_name": plant_name,
                "error": f"Processing failed: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    def _conduct_legal_entity_research(self, plant_name: str) -> dict:
        """Conduct research to identify the legal entity structure of the Polish power plant."""
        
        research_query = f"""
        Research the legal entity structure for the Polish coal-fired power plant: "{plant_name}"
        
        Please investigate and provide detailed information on:

        1. LEGAL ENTITY IDENTIFICATION:
           - Is this power plant operated through a separate Special Purpose Vehicle (SPV)?
           - Search Poland's official KRS registry at https://ekrs.ms.gov.pl
           - Look for Polish company types: Spółka Akcyjna (S.A.), Spółka z ograniczoną odpowiedzialnością (Sp. z o.o.)
           - If it's an SPV, what is the full legal entity name?

        2. KRS REGISTRATION DETAILS:
           - KRS number (Krajowy Rejestr Sądowy number) if available
           - Company type (S.A., Sp. z o.o., etc.)
           - Registered address in Poland
           - Registration court information

        3. OWNERSHIP STRUCTURE:
           - Is it owned by major Polish energy companies (PGE, Enea, Tauron, ZEPAK)?
           - Or is it part of a broader utility company?
           - Parent company information if applicable

        4. OPERATIONAL STRUCTURE:
           - Does it function as an independent company with separate financials?
           - Or is it integrated into a larger utility's operations?

        5. ADDITIONAL SOURCES:
           - Check official capacity/emissions registries
           - Energy company websites (PGE, Enea, Tauron, ZEPAK)
           - Polish energy sector reports
           - Government energy databases

        Please prioritize official Polish sources:
        - KRS registry (https://ekrs.ms.gov.pl)
        - Corporate websites of major Polish energy operators
        - Official Polish government energy databases
        - Polish regulatory filings

        Focus specifically on determining whether separate financial statements would be available for this plant.
        """
        
        return self.research_agent.research(research_query)
    
    def _conduct_financial_research(self, plant_name: str, legal_research: dict) -> dict:
        """Conduct research to find and retrieve financial information."""
        
        legal_content = legal_research.get("answer", "")
        
        financial_query = f"""
        Based on the legal entity research for "{plant_name}", now search for financial information:

        Previous Legal Research Results:
        {legal_content}

        Please investigate and retrieve:

        1. FINANCIAL STATEMENTS SEARCH:
           - Use the KRS number identified to search for annual financial statements
           - Look for Polish financial reporting databases
           - Search company websites for annual reports
           - Check Polish business databases

        2. FINANCIAL DATA RETRIEVAL:
           - If financial statements are found, extract key financial metrics:
             * Revenue/Turnover (Przychody)
             * Total Assets (Aktywa ogółem)
             * Equity (Kapitał własny)
             * Profit/Loss (Zysk/Strata)
             * Number of employees (Zatrudnienie)
           - Provide the most recent year's data available

        3. FINANCIAL STATEMENT TRANSLATION:
           - If Polish financial statements are found, translate key sections to English
           - Provide both Polish terms and English translations
           - Focus on main financial statement items:
             * Bilans (Balance Sheet)
             * Rachunek zysków i strat (Profit & Loss Statement)
             * Przepływy pieniężne (Cash Flow Statement)

        4. DATA SOURCES:
           - KRS database URLs if statements are found
           - Company annual report URLs
           - Polish business database information
           - Corporate websites with financial data

        If no separate financial statements exist (because it's part of a larger utility), clearly state:
        - The parent company name (PGE, Enea, Tauron, ZEPAK, etc.)
        - Confirmation that plant-level financials are consolidated
        - Where the consolidated financials can be found

        Please provide specific URLs, document references, and actual financial figures if found.
        Translate all Polish financial terms to English for clarity.
        """
        
        return self.research_agent.research(financial_query)
    
    def _extract_structured_data(self, plant_name: str, legal_research: dict, financial_research: dict) -> dict:
        """Extract structured data from research results."""
        
        legal_content = legal_research.get("answer", "")
        financial_content = financial_research.get("answer", "")
        
        extraction_prompt = f"""
        Based on the research content provided below about "{plant_name}", extract the following information and format it as a JSON object.
        
        Legal Entity Research:
        {legal_content}
        
        Financial Research:
        {financial_content}
        
        Extract these specific fields:
        
        1. is_separate_spv: Is this plant operated as a separate SPV? ("Yes" or "No")
        2. legal_entity_name: Full legal name of the entity (or "Not Available" if not separate)
        3. company_type: Type of company (S.A., Sp. z o.o., etc. or "Not Available")
        4. krs_number: KRS (Krajowy Rejestr Sądowy) number (or "Not Available")
        5. registered_address: Company registered address in Poland (or "Not Available")
        6. parent_company: Parent/owner company name (PGE, Enea, Tauron, ZEPAK, etc. or "Not Available")
        7. financial_statements_available: Are separate financial statements available? ("Yes" or "No")
        8. latest_revenue_pln: Most recent revenue figure in PLN (or "Not Available")
        9. latest_total_assets_pln: Most recent total assets in PLN (or "Not Available")
        10. latest_equity_pln: Most recent equity in PLN (or "Not Available")
        11. financial_year: Year of the financial data (or "Not Available")
        12. data_source: Source of financial data (KRS URL, company website, etc. or "Not Available")
        13. consolidation_status: If not separate, explain consolidation ("Consolidated into [Parent]" or "Not Available")
        14. polish_terms_translation: Key Polish financial terms found with English translations (or "Not Available")
        
        Respond ONLY with a valid JSON object in this exact format:
        {{
          "plant_name": "{plant_name}",
          "is_separate_spv": "...",
          "legal_entity_name": "...",
          "company_type": "...",
          "krs_number": "...",
          "registered_address": "...",
          "parent_company": "...",
          "financial_statements_available": "...",
          "latest_revenue_pln": "...",
          "latest_total_assets_pln": "...",
          "latest_equity_pln": "...",
          "financial_year": "...",
          "data_source": "...",
          "consolidation_status": "...",
          "polish_terms_translation": "..."
        }}
        
        Extract only information that is explicitly stated in the research content. Use "Not Available" for missing information.
        For financial figures, include currency (PLN) and specify the year.
        For data_source, provide specific URLs or document references if mentioned.
        For polish_terms_translation, provide key Polish financial terms with their English equivalents.
        """
        
        try:
            # Use research agent to extract structured data
            result = self.research_agent.research(extraction_prompt)
            response_text = result.get("answer", "")
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_text = json_match.group(0)
                structured_data = json.loads(json_text)
                
                # Add metadata
                structured_data["legal_sources_count"] = len(legal_research.get("sources", []))
                structured_data["financial_sources_count"] = len(financial_research.get("sources", []))
                structured_data["research_timestamp"] = datetime.now().isoformat()
                
                return structured_data
            else:
                return {
                    "plant_name": plant_name,
                    "error": "Failed to extract JSON from response",
                    "raw_response": response_text
                }
                
        except Exception as e:
            return {
                "plant_name": plant_name,
                "error": f"Data extraction failed: {str(e)}"
            }

def save_polish_results_to_excel(results: list, filename: str):
    """Save current Polish financial results to Excel file."""
    try:
        # Create DataFrame from results
        df_out = pd.DataFrame(results)
        
        # Define column mapping for better readability
        column_mapping = {
            "plant_name": "Plant Name",
            "is_separate_spv": "Separate SPV",
            "legal_entity_name": "Legal Entity Name",
            "company_type": "Company Type",
            "krs_number": "KRS Number",
            "registered_address": "Registered Address",
            "parent_company": "Parent Company",
            "financial_statements_available": "Financial Statements Available",
            "latest_revenue_pln": "Latest Revenue (PLN)",
            "latest_total_assets_pln": "Latest Total Assets (PLN)",
            "latest_equity_pln": "Latest Equity (PLN)",
            "financial_year": "Financial Year",
            "data_source": "Data Source",
            "consolidation_status": "Consolidation Status",
            "polish_terms_translation": "Polish Terms Translation"
        }
        
        # Select and rename columns
        available_columns = [col for col in column_mapping.keys() if col in df_out.columns]
        if available_columns:
            df_filtered = df_out[available_columns].copy()
            df_filtered = df_filtered.rename(columns=column_mapping)
        else:
            df_filtered = df_out.copy()
        
        # Save to Excel
        df_filtered.to_excel(filename, index=False)
        
    except Exception as e:
        print(f"⚠️ Warning: Failed to save to Excel: {e}")

def main():
    """Main function to run Polish plant financial research."""
    
    try:
        # Read plant names from CSV
        df = pd.read_csv("poland_powerplant_names.csv")
        plant_names = df['names'].tolist()
        
        print(f"🚀 Starting Polish Power Plant Financial Research")
        print(f"Using Gemini Research Agent with Polish financial databases")
        print(f"Searching: KRS Registry, PGE, Enea, Tauron, ZEPAK websites")
        print(f"Plants to research: {plant_names}")
        print(f"{'='*60}")
        
        # Initialize extractor
        extractor = PolishPlantFinancialsExtractor()
        
        # Create output filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"polish_plant_financials_{timestamp}.xlsx"
        
        results = []
        for i, plant in enumerate(plant_names):
            print(f"\n[{i+1}/{len(plant_names)}] {plant}")
            print("-" * 50)
            
            try:
                result = extractor.extract_plant_financials(plant)
                results.append(result)
                
                # Print key findings
                if "error" not in result:
                    print(f"✅ Separate SPV: {result.get('is_separate_spv', 'N/A')}")
                    print(f"✅ Legal Entity: {result.get('legal_entity_name', 'N/A')}")
                    print(f"✅ Company Type: {result.get('company_type', 'N/A')}")
                    print(f"✅ KRS Number: {result.get('krs_number', 'N/A')}")
                    print(f"✅ Parent Company: {result.get('parent_company', 'N/A')}")
                    print(f"✅ Financials Available: {result.get('financial_statements_available', 'N/A')}")
                    print(f"✅ Latest Revenue: {result.get('latest_revenue_pln', 'N/A')}")
                    print(f"✅ Sources: Legal({result.get('legal_sources_count', 0)}) + Financial({result.get('financial_sources_count', 0)})")
                else:
                    print(f"❌ Error: {result.get('error', 'Unknown error')}")
                
                # Save to Excel after each plant (incremental save)
                save_polish_results_to_excel(results, output_filename)
                print(f"💾 Progress saved to {output_filename} ({len(results)}/{len(plant_names)} completed)")
                    
            except Exception as e:
                error_result = {"plant_name": plant, "error": str(e)}
                results.append(error_result)
                print(f"❌ Error: {e}")
                
                # Save even error results
                save_polish_results_to_excel(results, output_filename)
                print(f"💾 Progress saved to {output_filename} ({len(results)}/{len(plant_names)} completed)")
            
            # Check if we've processed 50 plants and need a longer break
            if (i + 1) % 50 == 0 and i < len(plant_names) - 1:
                print(f"\n🛑 Processed {i + 1} plants. Taking 2-minute break before next batch...")
                print("⏳ Break started at:", datetime.now().strftime("%H:%M:%S"))
                time.sleep(120)  # 2 minute break
                print("✅ Break completed at:", datetime.now().strftime("%H:%M:%S"))
                print("🚀 Resuming Polish financial research...")
            # Regular delay between requests (except for the last one)
            elif i < len(plant_names) - 1:
                print("⏳ Waiting 10 seconds before next request...")
                time.sleep(10)  # Longer delay for complex financial research
        
        # Final save
        save_polish_results_to_excel(results, output_filename)
        
        print(f"\n{'='*60}")
        print("POLISH FINANCIAL RESEARCH COMPLETED")
        print(f"{'='*60}")
        print(f"✅ Results exported to: {output_filename}")
        
        # Print summary
        successful = len([r for r in results if "error" not in r])
        failed = len(results) - successful
        
        print(f"📊 Summary:")
        print(f"   • Successful: {successful}")
        print(f"   • Failed: {failed}")
        print(f"   • Total: {len(results)}")
        
        if successful > 0:
            separate_spvs = len([r for r in results if r.get("is_separate_spv") == "Yes"])
            with_financials = len([r for r in results if r.get("financial_statements_available") == "Yes"])
            print(f"   • Separate SPVs: {separate_spvs}")
            print(f"   • With Financial Statements: {with_financials}")
        
        print(f"\n🎉 Polish power plant financial research completed!")
        
    except FileNotFoundError:
        print("❌ Error: 'powerplant_names.csv' not found.")
        print("Please ensure the CSV file with Polish power plant names exists.")
        
    except Exception as e:
        print(f"❌ An error occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
