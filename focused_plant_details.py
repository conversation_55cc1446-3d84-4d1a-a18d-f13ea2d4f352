#!/usr/bin/env python3
"""
Focused Power Plant Details Extractor
Collects only three specific fields:
1. Is this captive or not
2. Status of plant (operational / mothballed / retired / decommissioned)
3. Latest unit's commencement date / Latest unit's operation date
"""

import os
import time
import pandas as pd
import json
import re
from datetime import datetime
from gemini_research_agent import GeminiResearchAgent
from dotenv import load_dotenv

load_dotenv()

class FocusedPlantDetailsExtractor:
    """Extract focused power plant details using Gemini Research Agent."""
    
    def __init__(self):
        self.research_agent = GeminiResearchAgent()
        
    def extract_plant_info(self, plant_name: str) -> dict:
        """
        Extract focused plant information using the research agent.
        
        Args:
            plant_name: Name of the power plant to research
            
        Returns:
            Dictionary containing the three specific fields
        """
        
        print(f"🔍 Processing: {plant_name}")
        
        try:
            # Step 1: Focused research
            research_result = self._conduct_focused_research(plant_name)
            
            # Step 2: Extract structured data
            structured_data = self._extract_structured_data(plant_name, research_result)
            
            return structured_data
            
        except Exception as e:
            print(f"❌ Error processing {plant_name}: {e}")
            return {
                "plant_name": plant_name,
                "error": f"Processing failed: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    def _conduct_focused_research(self, plant_name: str) -> dict:
        """Conduct focused research on the power plant for the three specific fields."""
        
        research_query = f"""
        Research specific information about the power plant: "{plant_name}"
        
        Please find and provide detailed information on the following THREE aspects only:

        1. CAPTIVE STATUS:
           - Is this power plant captive or merchant/independent?
           - A captive power plant is one that generates electricity primarily for its own use or for a specific industrial facility/company
           - A merchant/independent power plant sells electricity to the grid or multiple customers
           - Look for information about ownership, purpose, and electricity sales arrangements

        2. PLANT STATUS:
           - What is the current operational status of this power plant?
           - Classify as one of: operational, mothballed, retired, decommissioned
           - Operational: Currently generating electricity
           - Mothballed: Temporarily shut down but can be restarted
           - Retired: Permanently shut down but infrastructure may remain
           - Decommissioned: Permanently shut down and dismantled/demolished

        3. LATEST UNIT OPERATION DATE:
           - What is the commencement date or operation start date of the latest/most recent unit of this power plant?
           - If the plant has multiple units, provide the date when the most recent unit began operations
           - Include information about all unit dates if available to identify the latest one
           - Look for terms like "commercial operation date", "commissioning date", "start date", "online date"

        Please prioritize official sources, government databases, company websites, and industry reports for accurate information. Focus specifically on these three aspects only.
        """
        
        return self.research_agent.research(research_query)
    
    def _extract_structured_data(self, plant_name: str, research_result: dict) -> dict:
        """Extract structured data from research results."""
        
        research_content = research_result.get("answer", "")
        
        extraction_prompt = f"""
        Based on the research content provided below about "{plant_name}", extract the following information and format it as a JSON object.

        Extract these THREE specific fields in the EXACT format specified:

        1. is_captive: Is this power plant captive or not?
           - "Yes" if it generates electricity primarily for its own use or a specific industrial facility
           - "No" if it sells electricity to the grid or multiple customers (merchant/independent)
           - "No" if information is not found or unclear

        2. plant_status: Current operational status of the plant (use EXACT terms only)
           - "operational" if currently generating electricity
           - "mothballed" if temporarily shut down but can be restarted
           - "retired" if permanently shut down but infrastructure remains
           - "decommissioned" if permanently shut down and dismantled
           - "operational" if status is unclear (default assumption)

        3. latest_unit_operation_year: Latest unit's commencement/operation year ONLY
           - Extract ONLY the year (YYYY format) from any date found
           - If you find "2015-03-15", return only "2015"
           - If you find "March 2020", return only "2020"
           - "Not Available" if no date information is found

        Research Content:
        {research_content}

        Respond ONLY with a valid JSON object in this exact format:
        {{
          "plant_name": "{plant_name}",
          "is_captive": "...",
          "plant_status": "...",
          "latest_unit_operation_year": "..."
        }}

        IMPORTANT FORMATTING RULES:
        - is_captive: ONLY "Yes" or "No" (default to "No" if unclear)
        - plant_status: ONLY "operational", "mothballed", "retired", or "decommissioned" (default to "operational" if unclear)
        - latest_unit_operation_year: ONLY the 4-digit year (YYYY) or "Not Available"

        For is_captive, look for keywords like "captive power", "own use", "industrial facility", "merchant", "independent", "grid sales".
        For plant_status, look for current operational information, shutdown notices, decommissioning announcements.
        For latest_unit_operation_year, extract ONLY the year from commissioning dates, commercial operation dates, or start dates of the most recent unit.
        """
        
        try:
            # Use research agent to extract structured data
            result = self.research_agent.research(extraction_prompt)
            response_text = result.get("answer", "")
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_text = json_match.group(0)
                structured_data = json.loads(json_text)
                
                # Add metadata
                structured_data["research_sources_count"] = len(research_result.get("sources", []))
                structured_data["research_timestamp"] = datetime.now().isoformat()
                
                return structured_data
            else:
                return {
                    "plant_name": plant_name,
                    "error": "Failed to extract JSON from response",
                    "raw_response": response_text
                }
                
        except Exception as e:
            return {
                "plant_name": plant_name,
                "error": f"Data extraction failed: {str(e)}"
            }

def save_results_to_excel(results: list, filename: str):
    """Save current results to Excel file."""
    try:
        # Create DataFrame from results
        df_out = pd.DataFrame(results)

        # Filter to only include required columns
        required_columns = [
            "plant_name",
            "is_captive",
            "plant_status",
            "latest_unit_operation_year"
        ]

        # Rename columns to match user requirements
        column_mapping = {
            "plant_name": "Plant Name",
            "is_captive": "Is Captive (Yes/No)",
            "plant_status": "Plant Status",
            "latest_unit_operation_year": "Latest Unit Operation Year"
        }

        # Select only required columns that exist in the dataframe
        available_columns = [col for col in required_columns if col in df_out.columns]
        if available_columns:
            df_filtered = df_out[available_columns].copy()
            # Rename columns
            df_filtered = df_filtered.rename(columns=column_mapping)
        else:
            # If no standard columns exist, save raw data
            df_filtered = df_out.copy()

        # Save to Excel
        df_filtered.to_excel(filename, index=False)

    except Exception as e:
        print(f"⚠️ Warning: Failed to save to Excel: {e}")

def main():
    """Main function to run focused plant research."""

    try:
        # Read plant names from CSV
        df = pd.read_csv("powerplant_names.csv")
        plant_names = df['names'].tolist()

        print(f"🚀 Starting focused power plant research")
        print(f"Using Gemini Research Agent with web search capabilities")
        print(f"Collecting 3 specific fields: Captive Status, Plant Status, Latest Unit Operation Date")
        print(f"Plants to research: {plant_names}")
        print(f"{'='*60}")

        # Initialize extractor
        extractor = FocusedPlantDetailsExtractor()

        # Create output filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"focused_plant_info_{timestamp}.xlsx"

        results = []
        for i, plant in enumerate(plant_names):
            print(f"\n[{i+1}/{len(plant_names)}] {plant}")
            print("-" * 50)

            try:
                result = extractor.extract_plant_info(plant)
                results.append(result)

                # Print key findings
                if "error" not in result:
                    print(f"✅ Captive Status: {result.get('is_captive', 'N/A')}")
                    print(f"✅ Plant Status: {result.get('plant_status', 'N/A')}")
                    print(f"✅ Latest Unit Operation Year: {result.get('latest_unit_operation_year', 'N/A')}")
                    print(f"✅ Sources: {result.get('research_sources_count', 0)}")
                else:
                    print(f"❌ Error: {result.get('error', 'Unknown error')}")

                # Save to Excel after each plant (incremental save)
                save_results_to_excel(results, output_filename)
                print(f"💾 Progress saved to {output_filename} ({len(results)}/{len(plant_names)} completed)")

            except Exception as e:
                error_result = {"plant_name": plant, "error": str(e)}
                results.append(error_result)
                print(f"❌ Error: {e}")

                # Save even error results
                save_results_to_excel(results, output_filename)
                print(f"💾 Progress saved to {output_filename} ({len(results)}/{len(plant_names)} completed)")

            # Check if we've processed 100 plants and need a longer break
            if (i + 1) % 100 == 0 and i < len(plant_names) - 1:
                print(f"\n🛑 Processed {i + 1} plants. Taking 1-minute break before next batch...")
                print("⏳ Break started at:", datetime.now().strftime("%H:%M:%S"))
                time.sleep(120)  # 2 minute break
                print("✅ Break completed at:", datetime.now().strftime("%H:%M:%S"))
                print("🚀 Resuming processing...")
            # Regular delay between requests (except for the last one)
            elif i < len(plant_names) - 1:
                print("⏳ Waiting 5 seconds before next request...")
                time.sleep(5)
        
        # Final save (already saved incrementally, but ensure final version is saved)
        save_results_to_excel(results, output_filename)
        
        print(f"\n{'='*60}")
        print("FOCUSED RESEARCH COMPLETED")
        print(f"{'='*60}")
        print(f"✅ Results exported to: {output_filename}")
        
        # Print summary
        successful = len([r for r in results if "error" not in r])
        failed = len(results) - successful
        
        print(f"📊 Summary:")
        print(f"   • Successful: {successful}")
        print(f"   • Failed: {failed}")
        print(f"   • Total: {len(results)}")
        
        if successful > 0:
            total_sources = sum(r.get("research_sources_count", 0) for r in results if "error" not in r)
            print(f"   • Total sources found: {total_sources}")
        
        print(f"\n🎉 Focused research completed successfully!")
        print(f"📋 Fields collected:")
        print(f"   1. Is Captive (Yes/No)")
        print(f"   2. Plant Status (operational/mothballed/retired/decommissioned)")
        print(f"   3. Latest Unit Operation Year (YYYY format only)")
        
    except FileNotFoundError:
        print("❌ Error: 'powerplant_names.csv' not found.")
        print("Creating sample file for demonstration...")
        
        # Create sample data
        sample_data = {'names': ['Great Waste Coal power station']}
        df = pd.DataFrame(sample_data)
        df.to_csv("powerplant_names.csv", index=False)
        
        print("✅ Sample 'powerplant_names.csv' created. Please run the script again.")
        
    except Exception as e:
        print(f"❌ An error occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
