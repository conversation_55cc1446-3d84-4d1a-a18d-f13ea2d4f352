#!/usr/bin/env python3
"""
Enhanced Gemini Research Agent with Web Search and Source Tracking
Based on the LangGraph backend implementation
"""

import os
from typing import Dict, Any, List
from dotenv import load_dotenv
from google.genai import Client
from datetime import datetime

load_dotenv()

class GeminiResearchAgent:
    """Enhanced research agent using Google Gemini with web search and source tracking."""
    
    def __init__(self):
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GEMINI_API_KEY environment variable not set")
        
        # Use the Google GenAI client for web search capabilities
        self.client = Client(api_key=api_key)
        self.model_name = "gemini-2.0-flash"
    
    def research(self, question: str, **kwargs) -> Dict[str, Any]:
        """
        Perform research using Gemini model with web search and source tracking.
        
        Args:
            question: The research question to ask
            **kwargs: Additional parameters (ignored for compatibility)
            
        Returns:
            Dictionary with 'answer' key containing the research result and 'sources' with citations
        """
        try:
            # Format the prompt for web research
            current_date = self._get_current_date()
            formatted_prompt = f"""Conduct targeted Google Searches to gather the most recent, credible information on "{question}" and synthesize it into a verifiable text artifact.

Instructions:
- Query should ensure that the most current information is gathered. The current date is {current_date}.
- Conduct multiple, diverse searches to gather comprehensive information.
- PRIORITY: Always search gem.wiki (Global Energy Monitor wiki) first for power plant data - it contains the most accurate technical specifications, unit capacities, and operational details.
- Use search queries like "site:gem.wiki [power plant name]" to find GEM wiki data.
- Consolidate key findings while meticulously tracking the source(s) for each specific piece of information.
- The output should be a well-written summary or report based on your search findings. 
- Only include the information found in the search results, don't make up any information.
- Focus on factual, technical specifications and operational details.
- For power plant data, prioritize: 1) GEM wiki, 2) official sources, 3) technical specifications, 4) regulatory filings.

Research Topic:
{question}"""

            # Use Google Search tool with Gemini
            response = self.client.models.generate_content(
                model=self.model_name,
                contents=formatted_prompt,
                config={
                    "tools": [{"google_search": {}}],
                    "temperature": 0,
                }
            )
            
            # Extract sources from grounding metadata
            raw_sources = self._get_raw_sources(response)
            citations = self._extract_citations(response)
            
            # Get the response text
            answer_text = response.text if response.text else ""
            
            # Insert citation markers into the text
            modified_text = self._insert_citation_markers(answer_text, citations)
            
            return {
                "answer": modified_text,
                "sources": citations,
                "raw_sources": raw_sources
            }
            
        except Exception as e:
            print(f"Error in research: {e}")
            import traceback
            traceback.print_exc()
            return {
                "answer": f"Research failed: {str(e)}",
                "sources": [],
                "raw_sources": []
            }
    
    def _get_current_date(self) -> str:
        """Get current date in readable format."""
        return datetime.now().strftime("%B %d, %Y")
    
    def _get_raw_sources(self, response) -> List[Dict[str, str]]:
        """Extract raw source information for separate tracking."""
        sources = []
        
        if not response or not response.candidates:
            return sources
        
        candidate = response.candidates[0]
        if (
            not hasattr(candidate, "grounding_metadata")
            or not candidate.grounding_metadata
            or not hasattr(candidate.grounding_metadata, "grounding_chunks")
            or candidate.grounding_metadata.grounding_chunks is None
        ):
            return sources
        
        for i, chunk in enumerate(candidate.grounding_metadata.grounding_chunks):
            if hasattr(chunk, 'web'):
                sources.append({
                    "id": i + 1,
                    "title": chunk.web.title if hasattr(chunk.web, 'title') else f"Source {i+1}",
                    "url": chunk.web.uri if hasattr(chunk.web, 'uri') else "",
                    "snippet": getattr(chunk.web, 'snippet', '') if hasattr(chunk.web, 'snippet') else ""
                })
        
        return sources
    
    def _extract_citations(self, response) -> List[Dict[str, Any]]:
        """Extract and format citation information from Gemini response."""
        citations = []
        
        # Ensure response and necessary nested structures are present
        if not response or not response.candidates:
            return citations
        
        candidate = response.candidates[0]
        if (
            not hasattr(candidate, "grounding_metadata")
            or not candidate.grounding_metadata
            or not hasattr(candidate.grounding_metadata, "grounding_supports")
            or candidate.grounding_metadata.grounding_supports is None
        ):
            return citations
        
        # Create URL mapping for short URLs
        resolved_urls = self._resolve_urls(candidate.grounding_metadata.grounding_chunks)
        
        for support in candidate.grounding_metadata.grounding_supports:
            citation = {}
            
            # Ensure segment information is present
            if not hasattr(support, "segment") or support.segment is None:
                continue
            
            start_index = (
                support.segment.start_index
                if support.segment.start_index is not None
                else 0
            )
            
            if support.segment.end_index is None:
                continue
            
            citation["start_index"] = start_index
            citation["end_index"] = support.segment.end_index
            citation["segments"] = []
            
            if (
                hasattr(support, "grounding_chunk_indices")
                and support.grounding_chunk_indices
            ):
                for ind in support.grounding_chunk_indices:
                    try:
                        chunk = candidate.grounding_metadata.grounding_chunks[ind]
                        resolved_url = resolved_urls.get(chunk.web.uri, chunk.web.uri)
                        citation["segments"].append({
                            "label": chunk.web.title.split(".")[0] if chunk.web.title else f"Source {ind+1}",
                            "short_url": resolved_url,
                            "value": chunk.web.uri,
                        })
                    except (IndexError, AttributeError):
                        pass
            
            citations.append(citation)
        
        return citations
    
    def _resolve_urls(self, grounding_chunks) -> Dict[str, str]:
        """Create a map of long URLs to shorter identifiers."""
        resolved_map = {}
        
        if not grounding_chunks:
            return resolved_map
            
        for idx, chunk in enumerate(grounding_chunks):
            if hasattr(chunk, 'web') and hasattr(chunk.web, 'uri'):
                # Create a shorter identifier
                resolved_map[chunk.web.uri] = f"[{idx+1}]"
        return resolved_map
    
    def _insert_citation_markers(self, text: str, citations: List[Dict[str, Any]]) -> str:
        """Insert citation markers into text based on grounding metadata."""
        if not citations:
            return text
        
        # Sort citations by end_index in descending order
        sorted_citations = sorted(
            citations, key=lambda c: (c["end_index"], c["start_index"]), reverse=True
        )
        
        modified_text = text
        for citation_info in sorted_citations:
            end_idx = citation_info["end_index"]
            marker_to_insert = ""
            for segment in citation_info["segments"]:
                marker_to_insert += f" [{segment['label']}]({segment['value']})"
            
            # Insert the citation marker at the end position
            modified_text = (
                modified_text[:end_idx] + marker_to_insert + modified_text[end_idx:]
            )
        
        return modified_text