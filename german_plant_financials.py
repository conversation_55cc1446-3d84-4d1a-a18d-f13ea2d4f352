#!/usr/bin/env python3
"""
German Power Plant Financial Information Extractor
Identifies legal entities and retrieves financial information for coal/thermal power plants in Germany.
Focuses on SPVs, Handelsregister data, and financial statements from Bundesanzeiger.
"""

import os
import time
import pandas as pd
import json
import re
from datetime import datetime
from gemini_research_agent import GeminiResearchAgent
from dotenv import load_dotenv

load_dotenv()

class GermanPlantFinancialsExtractor:
    """Extract financial information for German power plants using Gemini Research Agent."""
    
    def __init__(self):
        self.research_agent = GeminiResearchAgent()
        
    def extract_plant_financials(self, plant_name: str) -> dict:
        """
        Extract financial and legal entity information for a German power plant.
        
        Args:
            plant_name: Name of the German power plant to research
            
        Returns:
            Dictionary containing legal entity and financial information
        """
        
        print(f"🔍 Processing German Plant: {plant_name}")
        
        try:
            # Step 1: Legal entity identification research
            legal_research = self._conduct_legal_entity_research(plant_name)
            
            # Step 2: Financial information research
            financial_research = self._conduct_financial_research(plant_name, legal_research)
            
            # Step 3: Extract structured data
            structured_data = self._extract_structured_data(plant_name, legal_research, financial_research)
            
            return structured_data
            
        except Exception as e:
            print(f"❌ Error processing {plant_name}: {e}")
            return {
                "plant_name": plant_name,
                "error": f"Processing failed: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    def _conduct_legal_entity_research(self, plant_name: str) -> dict:
        """Conduct research to identify the legal entity structure of the power plant."""
        
        research_query = f"""
        Research the legal entity structure for the German power plant: "{plant_name}"
        
        Please investigate and provide detailed information on:

        1. LEGAL ENTITY IDENTIFICATION:
           - Is this power plant operated through a distinct legal entity (SPV)?
           - Search Germany's official company register (Handelsregister) at https://www.handelsregister.de
           - Look for company types: GmbH, AG, GmbH & Co. KG, or other German corporate structures
           - If it's an SPV, what is the full legal entity name?

        2. COMPANY REGISTRATION DETAILS:
           - Handelsregister (HRB) number if available
           - Registergericht (registration court) information
           - Company type (GmbH, AG, etc.)
           - Registered address

        3. OWNERSHIP STRUCTURE:
           - Is it owned by major German energy operators (RWE, EnBW, LEAG, Uniper, Vattenfall)?
           - Or is it part of a broader utility company?
           - Parent company information if applicable

        4. OPERATIONAL STRUCTURE:
           - Does it function as an independent company with separate financials?
           - Or is it integrated into a larger utility's operations?

        Please prioritize official German sources:
        - Handelsregister.de (official company register)
        - Corporate websites of major German energy operators
        - Official company announcements
        - German regulatory filings

        Focus specifically on determining whether separate financial statements would be available for this plant.
        """
        
        return self.research_agent.research(research_query)
    
    def _conduct_financial_research(self, plant_name: str, legal_research: dict) -> dict:
        """Conduct research to find and retrieve financial information."""
        
        legal_content = legal_research.get("answer", "")
        
        financial_query = f"""
        Based on the legal entity research for "{plant_name}", now search for financial information:

        Previous Legal Research Results:
        {legal_content}

        Please investigate and retrieve:

        1. FINANCIAL STATEMENTS SEARCH:
           - Search Bundesanzeiger (German Federal Gazette) for annual financial statements
           - Look for the specific legal entity identified in the previous research
           - Search using HRB number or company name if available

        2. FINANCIAL DATA RETRIEVAL:
           - If financial statements are found, extract key financial metrics:
             * Revenue/Turnover (Umsatz)
             * Total Assets (Bilanzsumme)
             * Equity (Eigenkapital)
             * Profit/Loss (Gewinn/Verlust)
             * Number of employees (Mitarbeiter)
           - Provide the most recent year's data available

        3. FINANCIAL STATEMENT TRANSLATION:
           - If German financial statements are found, translate key sections to English
           - Provide both German terms and English translations
           - Focus on main financial statement items

        4. DATA SOURCES:
           - Bundesanzeiger URLs if statements are found
           - Orbis database information if available
           - Corporate annual reports if accessible

        If no separate financial statements exist (because it's part of a larger utility), clearly state:
        - The parent company name
        - Confirmation that plant-level financials are consolidated
        - Where the consolidated financials can be found

        Please provide specific URLs, document references, and actual financial figures if found.
        """
        
        return self.research_agent.research(financial_query)
    
    def _extract_structured_data(self, plant_name: str, legal_research: dict, financial_research: dict) -> dict:
        """Extract structured data from research results."""
        
        legal_content = legal_research.get("answer", "")
        financial_content = financial_research.get("answer", "")
        
        extraction_prompt = f"""
        Based on the research content provided below about "{plant_name}", extract the following information and format it as a JSON object.
        
        Legal Entity Research:
        {legal_content}
        
        Financial Research:
        {financial_content}
        
        Extract these specific fields:
        
        1. is_separate_entity: Is this plant operated as a separate legal entity/SPV? ("Yes" or "No")
        2. legal_entity_name: Full legal name of the entity (or "Not Available" if not separate)
        3. company_type: Type of company (GmbH, AG, etc. or "Not Available")
        4. hrb_number: Handelsregister (HRB) number (or "Not Available")
        5. registergericht: Registration court (or "Not Available")
        6. parent_company: Parent/owner company name (or "Not Available")
        7. financial_statements_available: Are separate financial statements available? ("Yes" or "No")
        8. latest_revenue: Most recent revenue figure with currency (or "Not Available")
        9. latest_total_assets: Most recent total assets with currency (or "Not Available")
        10. latest_equity: Most recent equity with currency (or "Not Available")
        11. financial_year: Year of the financial data (or "Not Available")
        12. data_source: Source of financial data (Bundesanzeiger URL, etc. or "Not Available")
        13. consolidation_status: If not separate, explain consolidation ("Consolidated into [Parent]" or "Not Available")
        
        Respond ONLY with a valid JSON object in this exact format:
        {{
          "plant_name": "{plant_name}",
          "is_separate_entity": "...",
          "legal_entity_name": "...",
          "company_type": "...",
          "hrb_number": "...",
          "registergericht": "...",
          "parent_company": "...",
          "financial_statements_available": "...",
          "latest_revenue": "...",
          "latest_total_assets": "...",
          "latest_equity": "...",
          "financial_year": "...",
          "data_source": "...",
          "consolidation_status": "..."
        }}
        
        Extract only information that is explicitly stated in the research content. Use "Not Available" for missing information.
        For financial figures, include currency (EUR) and specify the year.
        For data_source, provide specific URLs or document references if mentioned.
        """
        
        try:
            # Use research agent to extract structured data
            result = self.research_agent.research(extraction_prompt)
            response_text = result.get("answer", "")
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_text = json_match.group(0)
                structured_data = json.loads(json_text)
                
                # Add metadata
                structured_data["legal_sources_count"] = len(legal_research.get("sources", []))
                structured_data["financial_sources_count"] = len(financial_research.get("sources", []))
                structured_data["research_timestamp"] = datetime.now().isoformat()
                
                return structured_data
            else:
                return {
                    "plant_name": plant_name,
                    "error": "Failed to extract JSON from response",
                    "raw_response": response_text
                }
                
        except Exception as e:
            return {
                "plant_name": plant_name,
                "error": f"Data extraction failed: {str(e)}"
            }

def save_financial_results_to_excel(results: list, filename: str):
    """Save current financial results to Excel file."""
    try:
        # Create DataFrame from results
        df_out = pd.DataFrame(results)
        
        # Define column mapping for better readability
        column_mapping = {
            "plant_name": "Plant Name",
            "is_separate_entity": "Separate Legal Entity",
            "legal_entity_name": "Legal Entity Name",
            "company_type": "Company Type",
            "hrb_number": "HRB Number",
            "registergericht": "Registration Court",
            "parent_company": "Parent Company",
            "financial_statements_available": "Financial Statements Available",
            "latest_revenue": "Latest Revenue",
            "latest_total_assets": "Latest Total Assets",
            "latest_equity": "Latest Equity",
            "financial_year": "Financial Year",
            "data_source": "Data Source",
            "consolidation_status": "Consolidation Status"
        }
        
        # Select and rename columns
        available_columns = [col for col in column_mapping.keys() if col in df_out.columns]
        if available_columns:
            df_filtered = df_out[available_columns].copy()
            df_filtered = df_filtered.rename(columns=column_mapping)
        else:
            df_filtered = df_out.copy()
        
        # Save to Excel
        df_filtered.to_excel(filename, index=False)
        
    except Exception as e:
        print(f"⚠️ Warning: Failed to save to Excel: {e}")

def main():
    """Main function to run German plant financial research."""
    
    try:
        # Read plant names from CSV
        df = pd.read_csv("german_powerplant_names.csv")
        plant_names = df['names'].tolist()
        
        print(f"🚀 Starting German Power Plant Financial Research")
        print(f"Using Gemini Research Agent with German financial databases")
        print(f"Searching: Handelsregister, Bundesanzeiger, Corporate websites")
        print(f"Plants to research: {plant_names}")
        print(f"{'='*60}")
        
        # Initialize extractor
        extractor = GermanPlantFinancialsExtractor()
        
        # Create output filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"german_plant_financials_{timestamp}.xlsx"
        
        results = []
        for i, plant in enumerate(plant_names):
            print(f"\n[{i+1}/{len(plant_names)}] {plant}")
            print("-" * 50)
            
            try:
                result = extractor.extract_plant_financials(plant)
                results.append(result)
                
                # Print key findings
                if "error" not in result:
                    print(f"✅ Separate Entity: {result.get('is_separate_entity', 'N/A')}")
                    print(f"✅ Legal Entity: {result.get('legal_entity_name', 'N/A')}")
                    print(f"✅ Company Type: {result.get('company_type', 'N/A')}")
                    print(f"✅ Parent Company: {result.get('parent_company', 'N/A')}")
                    print(f"✅ Financials Available: {result.get('financial_statements_available', 'N/A')}")
                    print(f"✅ Latest Revenue: {result.get('latest_revenue', 'N/A')}")
                    print(f"✅ Sources: Legal({result.get('legal_sources_count', 0)}) + Financial({result.get('financial_sources_count', 0)})")
                else:
                    print(f"❌ Error: {result.get('error', 'Unknown error')}")
                
                # Save to Excel after each plant (incremental save)
                save_financial_results_to_excel(results, output_filename)
                print(f"💾 Progress saved to {output_filename} ({len(results)}/{len(plant_names)} completed)")
                    
            except Exception as e:
                error_result = {"plant_name": plant, "error": str(e)}
                results.append(error_result)
                print(f"❌ Error: {e}")
                
                # Save even error results
                save_financial_results_to_excel(results, output_filename)
                print(f"💾 Progress saved to {output_filename} ({len(results)}/{len(plant_names)} completed)")
            
            # Check if we've processed 50 plants and need a longer break (smaller batches for complex research)
            if (i + 1) % 50 == 0 and i < len(plant_names) - 1:
                print(f"\n🛑 Processed {i + 1} plants. Taking 2-minute break before next batch...")
                print("⏳ Break started at:", datetime.now().strftime("%H:%M:%S"))
                time.sleep(120)  # 2 minute break
                print("✅ Break completed at:", datetime.now().strftime("%H:%M:%S"))
                print("🚀 Resuming financial research...")
            # Regular delay between requests (except for the last one)
            elif i < len(plant_names) - 1:
                print("⏳ Waiting 10 seconds before next request...")
                time.sleep(10)  # Longer delay for complex financial research
        
        # Final save
        save_financial_results_to_excel(results, output_filename)
        
        print(f"\n{'='*60}")
        print("GERMAN FINANCIAL RESEARCH COMPLETED")
        print(f"{'='*60}")
        print(f"✅ Results exported to: {output_filename}")
        
        # Print summary
        successful = len([r for r in results if "error" not in r])
        failed = len(results) - successful
        
        print(f"📊 Summary:")
        print(f"   • Successful: {successful}")
        print(f"   • Failed: {failed}")
        print(f"   • Total: {len(results)}")
        
        if successful > 0:
            separate_entities = len([r for r in results if r.get("is_separate_entity") == "Yes"])
            with_financials = len([r for r in results if r.get("financial_statements_available") == "Yes"])
            print(f"   • Separate Legal Entities: {separate_entities}")
            print(f"   • With Financial Statements: {with_financials}")
        
        print(f"\n🎉 German power plant financial research completed!")
        
    except FileNotFoundError:
        print("❌ Error: 'powerplant_names.csv' not found.")
        print("Please ensure the CSV file with German power plant names exists.")
        
    except Exception as e:
        print(f"❌ An error occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
