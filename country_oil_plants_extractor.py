#!/usr/bin/env python3
"""
Country Oil Power Plants Extractor
Takes a country name as input and extracts:
1. All oil-fired power plant names in that country
2. Captive status (Yes/No)
3. Plant status (operational/mothballed/retired/decommissioned)
4. Latest unit operation year (YYYY format only)
"""

import os
import time
import pandas as pd
import json
import re
from datetime import datetime
from gemini_research_agent import GeminiResearchAgent
from dotenv import load_dotenv

load_dotenv()

class CountryOilPlantExtractor:
    """Extract oil-fired power plant information for a specific country."""
    
    def __init__(self):
        self.research_agent = GeminiResearchAgent()
        
    def extract_country_oil_plants(self, country_name: str, max_retries: int = 3) -> dict:
        """
        Extract all oil-fired power plants for a given country.
        
        Args:
            country_name: Name of the country to research
            max_retries: Maximum number of retry attempts for 429 errors
            
        Returns:
            Dictionary containing list of oil plants and their details
        """
        
        print(f"🔍 Researching Oil Power Plants in: {country_name}")
        
        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    wait_time = 30 * (2 ** attempt)  # Exponential backoff
                    print(f"⏳ Retry attempt {attempt}/{max_retries}. Waiting {wait_time} seconds...")
                    time.sleep(wait_time)
                
                # Step 1: Get count of oil power plants in the country
                count_research = self._get_oil_plants_count(country_name)

                # Add delay between research steps
                time.sleep(10)

                # Step 2: Extract plant names
                names_research = self._extract_oil_plant_names(country_name)

                # Add delay between research steps
                time.sleep(15)

                # Step 3: Extract detailed plant information
                plants_data = self._extract_plants_data(country_name, count_research, names_research)
                
                return plants_data
                
            except Exception as e:
                error_msg = str(e).lower()
                if "429" in error_msg or "rate limit" in error_msg or "too many requests" in error_msg:
                    if attempt < max_retries:
                        print(f"⚠️ Rate limit hit (429 error). Attempt {attempt + 1}/{max_retries + 1}")
                        continue
                    else:
                        print(f"❌ Max retries exceeded for rate limiting: {country_name}")
                        return {
                            "country_name": country_name,
                            "error": f"Rate limit exceeded after {max_retries + 1} attempts: {str(e)}",
                            "timestamp": datetime.now().isoformat()
                        }
                else:
                    print(f"❌ Error processing {country_name}: {e}")
                    return {
                        "country_name": country_name,
                        "error": f"Processing failed: {str(e)}",
                        "timestamp": datetime.now().isoformat()
                    }
    
    def _get_oil_plants_count(self, country_name: str) -> dict:
        """Get the total count of oil-fired power plants in the specified country."""

        count_query = f"How many oil power plants are there in {country_name}? Provide the total number of oil-fired power plants including all types (Heavy Fuel Oil, Light Fuel Oil, Diesel, Crude Oil plants)."

        print(f"📊 Getting count of oil power plants in {country_name}...")
        result = self.research_agent.research(count_query)

        # Extract and display count information
        count_info = result.get("answer", "")
        print(f"🔢 Count Information: {count_info[:200]}...")

        return result

    def _extract_oil_plant_names(self, country_name: str) -> dict:
        """Extract names of all oil-fired power plants in the specified country."""

        names_query = f"List me out all the oil power plants present in {country_name}. Provide the complete list of names of oil-fired power plants including Heavy Fuel Oil, Light Fuel Oil, Diesel, and Crude Oil power plants."

        print(f"📝 Extracting oil power plant names in {country_name}...")
        result = self.research_agent.research(names_query)

        # Extract and display some plant names
        names_info = result.get("answer", "")
        print(f"🏭 Plant Names Found: {names_info[:300]}...")

        return result
    
    def _extract_plants_data(self, country_name: str, count_research: dict, names_research: dict) -> dict:
        """Extract structured data for all plants found."""

        count_content = count_research.get("answer", "")
        names_content = names_research.get("answer", "")

        # Combine count and names information
        combined_content = f"""
        COUNT INFORMATION:
        {count_content}

        PLANT NAMES INFORMATION:
        {names_content}
        """

        print(f"🔍 Extracting detailed information for oil power plants in {country_name}...")
        
        extraction_prompt = f"""
        Based on the research content about oil-fired power plants in {country_name}, extract detailed information for each plant.

        Research Content:
        {combined_content}
        
        For EACH oil-fired power plant mentioned, extract the following information:
        
        1. plant_name: Official name of the power plant
        2. is_captive: Is this plant captive or merchant? 
           - "Yes" if it generates electricity primarily for its own use or a specific industrial facility
           - "No" if it sells electricity to the grid or multiple customers
           - "No" if information is unclear (default assumption)
        
        3. plant_status: Current operational status (use EXACT terms only)
           - "operational" if currently generating electricity
           - "mothballed" if temporarily shut down but can be restarted
           - "retired" if permanently shut down but infrastructure remains
           - "decommissioned" if permanently shut down and dismantled
           - "operational" if status is unclear (default assumption)
        
        4. latest_unit_operation_year: Latest unit's operation year ONLY
           - Extract ONLY the year (YYYY format) from any commissioning/operation date
           - "Not Available" if no date information is found
        
        5. location: Location/region within {country_name}
        6. capacity_mw: Approximate capacity in MW (or "Not Available")
        7. owner_operator: Owner/operator company name (or "Not Available")
        8. fuel_type: Type of oil fuel used (Heavy Fuel Oil, Diesel, Crude Oil, etc. or "Not Available")
        
        Respond ONLY with a valid JSON object in this exact format:
        {{
          "country_name": "{country_name}",
          "total_plants_found": [number],
          "plants": [
            {{
              "plant_name": "...",
              "is_captive": "...",
              "plant_status": "...",
              "latest_unit_operation_year": "...",
              "location": "...",
              "capacity_mw": "...",
              "owner_operator": "...",
              "fuel_type": "..."
            }},
            {{
              "plant_name": "...",
              "is_captive": "...",
              "plant_status": "...",
              "latest_unit_operation_year": "...",
              "location": "...",
              "capacity_mw": "...",
              "owner_operator": "...",
              "fuel_type": "..."
            }}
          ]
        }}
        
        IMPORTANT FORMATTING RULES:
        - is_captive: ONLY "Yes" or "No" (default to "No" if unclear)
        - plant_status: ONLY "operational", "mothballed", "retired", or "decommissioned" (default to "operational" if unclear)
        - latest_unit_operation_year: ONLY the 4-digit year (YYYY) or "Not Available"
        
        Extract information for ALL oil-fired plants mentioned in the research content. Include plants using:
        - Heavy Fuel Oil (HFO)
        - Light Fuel Oil (LFO)
        - Diesel oil
        - Crude oil
        - Residual fuel oil
        - Any petroleum-based liquid fuel
        """
        
        try:
            # Use research agent to extract structured data
            result = self.research_agent.research(extraction_prompt)
            response_text = result.get("answer", "")
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_text = json_match.group(0)
                structured_data = json.loads(json_text)
                
                # Add metadata
                count_sources = len(count_research.get("sources", []))
                names_sources = len(names_research.get("sources", []))
                structured_data["research_sources_count"] = count_sources + names_sources
                structured_data["research_timestamp"] = datetime.now().isoformat()
                
                return structured_data
            else:
                return {
                    "country_name": country_name,
                    "error": "Failed to extract JSON from response",
                    "raw_response": response_text
                }
                
        except Exception as e:
            return {
                "country_name": country_name,
                "error": f"Data extraction failed: {str(e)}"
            }

def save_country_oil_results_to_excel(country_data: dict, filename: str):
    """Save country oil plant results to Excel file."""
    try:
        if "plants" in country_data and country_data["plants"]:
            # Create DataFrame from plants list
            plants_list = country_data["plants"]
            df_plants = pd.DataFrame(plants_list)
            
            # Add country information to each row
            df_plants["Country"] = country_data.get("country_name", "Unknown")
            df_plants["Total Plants Found"] = country_data.get("total_plants_found", len(plants_list))
            df_plants["Research Timestamp"] = country_data.get("research_timestamp", "Unknown")
            
            # Rename columns for better readability
            column_mapping = {
                "plant_name": "Plant Name",
                "is_captive": "Is Captive (Yes/No)",
                "plant_status": "Plant Status",
                "latest_unit_operation_year": "Latest Unit Operation Year",
                "location": "Location",
                "capacity_mw": "Capacity (MW)",
                "owner_operator": "Owner/Operator",
                "fuel_type": "Fuel Type",
                "Country": "Country",
                "Total Plants Found": "Total Plants Found",
                "Research Timestamp": "Research Timestamp"
            }
            
            # Reorder columns
            column_order = [
                "Country", "Plant Name", "Is Captive (Yes/No)", "Plant Status", 
                "Latest Unit Operation Year", "Location", "Capacity (MW)", 
                "Owner/Operator", "Fuel Type", "Total Plants Found", "Research Timestamp"
            ]
            
            df_plants = df_plants.rename(columns=column_mapping)
            
            # Select columns that exist
            available_columns = [col for col in column_order if col in df_plants.columns]
            df_final = df_plants[available_columns]
            
            # Save to Excel
            df_final.to_excel(filename, index=False)
        else:
            # Handle case where no plants found or error occurred
            error_data = {
                "Country": [country_data.get("country_name", "Unknown")],
                "Error": [country_data.get("error", "No plants found")],
                "Timestamp": [country_data.get("timestamp", datetime.now().isoformat())]
            }
            df_error = pd.DataFrame(error_data)
            df_error.to_excel(filename, index=False)
        
    except Exception as e:
        print(f"⚠️ Warning: Failed to save to Excel: {e}")

def main():
    """Main function to run country oil plant research."""
    
    try:
        # Get country name from user input
        country_name = input("🌍 Enter the country name to research oil power plants: ").strip()
        
        if not country_name:
            print("❌ Error: Please provide a country name.")
            return
        
        print(f"🚀 Starting Oil Power Plant Research for: {country_name}")
        print(f"Using Gemini Research Agent with comprehensive energy databases")
        print(f"Searching for: Heavy Fuel Oil, Light Fuel Oil, Diesel, Crude Oil plants")
        print(f"{'='*60}")
        print(f"📋 Research Process:")
        print(f"   Step 1: Get count of oil power plants")
        print(f"   Step 2: Extract plant names")
        print(f"   Step 3: Get detailed information (captive status, operational status, etc.)")
        print(f"{'='*60}")

        # Initialize extractor
        extractor = CountryOilPlantExtractor()

        # Create output filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_country_name = re.sub(r'[^\w\s-]', '', country_name).strip().replace(' ', '_')
        output_filename = f"{safe_country_name}_oil_plants_{timestamp}.xlsx"

        print(f"🔍 Starting staged research for oil-fired power plants in {country_name}...")
        
        try:
            result = extractor.extract_country_oil_plants(country_name)
            
            # Print summary
            if "error" not in result:
                plants_found = result.get("total_plants_found", 0)
                plants_list = result.get("plants", [])
                
                print(f"\n✅ Research completed successfully!")
                print(f"📊 Summary:")
                print(f"   • Country: {country_name}")
                print(f"   • Total Oil Plants Found: {plants_found}")
                print(f"   • Plants with Details: {len(plants_list)}")
                print(f"   • Sources Used: {result.get('research_sources_count', 0)}")
                
                if plants_list:
                    print(f"\n🏭 Sample Plants Found:")
                    for i, plant in enumerate(plants_list[:5]):  # Show first 5 plants
                        fuel_info = plant.get('fuel_type', 'Unknown')
                        print(f"   {i+1}. {plant.get('plant_name', 'Unknown')} - {plant.get('plant_status', 'Unknown')} ({plant.get('capacity_mw', 'Unknown')} MW, {fuel_info})")
                    
                    if len(plants_list) > 5:
                        print(f"   ... and {len(plants_list) - 5} more plants")
            else:
                print(f"❌ Error: {result.get('error', 'Unknown error')}")
            
            # Save to Excel
            save_country_oil_results_to_excel(result, output_filename)
            print(f"\n💾 Results saved to: {output_filename}")
            
        except Exception as e:
            error_result = {"country_name": country_name, "error": str(e)}
            save_country_oil_results_to_excel(error_result, output_filename)
            print(f"❌ Error: {e}")
            print(f"💾 Error details saved to: {output_filename}")
        
        print(f"\n🎉 Oil power plant research completed for {country_name}!")
        
    except KeyboardInterrupt:
        print("\n⚠️ Research interrupted by user.")
    except Exception as e:
        print(f"❌ An error occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
