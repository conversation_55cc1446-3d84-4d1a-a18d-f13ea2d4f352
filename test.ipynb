{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["⚠️ Error 429: {\n", "    \"error\": {\n", "        \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.\",\n", "        \"type\": \"insufficient_quota\",\n", "        \"param\": null,\n", "        \"code\": \"insufficient_quota\"\n", "    }\n", "}\n", "\n"]}], "source": ["import requests\n", "\n", "# Replace with your OpenAI API key\n", "api_key = \"********************************************************************************************************************************************************************\"\n", "\n", "# Endpoint for Chat Completion\n", "url = \"https://api.openai.com/v1/chat/completions\"\n", "\n", "# Request headers\n", "headers = {\n", "    \"Authorization\": f\"Bearer {api_key}\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "# Request body\n", "data = {\n", "    \"model\": \"gpt-3.5-turbo\",  # or \"gpt-4\" if you have access\n", "    \"messages\": [\n", "        {\"role\": \"user\", \"content\": \"Hello!\"}\n", "    ],\n", "    \"temperature\": 0.5\n", "}\n", "\n", "# Send the request\n", "response = requests.post(url, headers=headers, json=data)\n", "\n", "# Check and print result\n", "if response.status_code == 200:\n", "    print(\"✅ API Key is valid. Response:\")\n", "    print(response.json()['choices'][0]['message']['content'])\n", "elif response.status_code == 401:\n", "    print(\"❌ Invalid or unauthorized API key.\")\n", "else:\n", "    print(f\"⚠️ Error {response.status_code}: {response.text}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}