#!/usr/bin/env python3
"""
Power Plant Capacity and Status Extractor using Gemini Research Agent
Extracts total capacity and operational status for power plants
"""

import os
import time
import pandas as pd
import json
import re
from datetime import datetime
from gemini_research_agent import GeminiResearchAgent
from dotenv import load_dotenv

load_dotenv()

class CapacityStatusExtractor:
    """Extract total capacity and operational status using Gemini Research Agent."""
    
    def __init__(self):
        self.research_agent = GeminiResearchAgent()
        
    def extract_plant_info(self, plant_name: str) -> dict:
        """
        Extract total capacity and operational status information.
        
        Args:
            plant_name: Name of the power plant to research
            
        Returns:
            Dictionary containing structured plant information
        """
        
        print(f"🔍 Processing: {plant_name}")
        
        try:
            # Step 1: Research capacity and status information
            research_result = self._conduct_capacity_status_research(plant_name)
            
            # Step 2: Extract and process structured data
            structured_data = self._extract_structured_data(plant_name, research_result)
            
            return structured_data
            
        except Exception as e:
            print(f"❌ Error processing {plant_name}: {e}")
            return {
                "plant_name": plant_name,
                "error": f"Processing failed: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    def _conduct_capacity_status_research(self, plant_name: str) -> dict:
        """Conduct focused research on capacity and operational status."""
        
        research_query = f"""
        Research detailed information about the power plant: "{plant_name}"
        
        Please find and provide comprehensive information on the following aspects:

        1. UNIT CAPACITIES:
           - What is the capacity of each individual unit in this power plant?
           - List all units with their specific capacities in MW (megawatts)
           - If there are multiple units, provide capacity for each unit separately
           - Include any historical capacity information and current capacity
           - Total installed capacity of the entire power plant

        2. UNIT OPERATIONAL STATUS:
           - What is the current operational status of each individual unit?
           - Is each unit currently operational, retired, decommissioned, or under maintenance?
           - Provide status for each unit separately if the plant has multiple units
           - Any recent changes in unit operational status
           - Overall plant operational status

        Please prioritize official sources, government databases, company websites, utility company reports, and industry databases for accurate information. Focus specifically on getting detailed unit-level information for both capacity and operational status.
        """
        
        return self.research_agent.research(research_query)
    
    def _extract_structured_data(self, plant_name: str, research_result: dict) -> dict:
        """Extract and process structured data from research results."""
        
        research_content = research_result.get("answer", "")
        
        extraction_prompt = f"""
        Based on the research content provided below about "{plant_name}", extract the following information and format it as a JSON object.
        
        Extract these specific fields:
        1. units_info: List all units with their capacities and operational status
        2. total_capacity_mw: Sum of all unit capacities in MW (just the number, no "MW" text)
        3. overall_operational_status: Based on all units' status using these rules:
           - If atleast one unit is operational -> "Operational"
           - If ALL units are retired -> "Retired" 
           - If ALL units are decommissioned -> "Decommissioned"
           - If mixed retired/decommissioned -> "Retired"
           - If status unclear -> "Not Available"
        
        IMPORTANT CAPACITY CALCULATION RULES:
        - Include capacity of ALL units that have been built and operated (operational + retired + decommissioned)
        - DO NOT include capacity of units that are only announced, planned, proposed, or under construction
        - DO NOT include future units that haven't started operations yet
        - Only count units that have actually generated power at some point in their history
        
        Research Content:
        {research_content}
        
        Respond ONLY with a valid JSON object in this exact format:
        {{
          "plant_name": "{plant_name}",
          "units_info": [
            {{
              "unit_name": "Unit 1",
              "capacity_mw": 150,
              "operational_status": "Operational",
              "include_in_total": true
            }},
            {{
              "unit_name": "Unit 2", 
              "capacity_mw": 150,
              "operational_status": "Retired",
              "include_in_total": true
            }},
            {{
              "unit_name": "Unit 3", 
              "capacity_mw": 200,
              "operational_status": "Announced",
              "include_in_total": false
            }}
          ],
          "total_capacity_mw": 300,
          "overall_operational_status": "Operational"
        }}
        
        Important rules:
        - For total_capacity_mw: Sum only units where include_in_total is true
        - For overall_operational_status: Use exactly "Operational", "Retired", "Decommissioned", or "Not Available"
        - Set include_in_total to true for: Operational, Retired, Decommissioned units
        - Set include_in_total to false for: Announced, Planned, Proposed, Under Construction, Future units
        - If you find capacity information but no unit breakdown, create a single unit entry
        - If no capacity information is found, use 0 for total_capacity_mw
        - Extract only information that is explicitly stated in the research content
        """
        
        try:
            # Use research agent to extract structured data
            result = self.research_agent.research(extraction_prompt)
            response_text = result.get("answer", "")
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_text = json_match.group(0)
                structured_data = json.loads(json_text)
                
                # Validate and process the data
                processed_data = self._process_extracted_data(structured_data)
                
                # Add metadata
                processed_data["research_sources_count"] = len(research_result.get("sources", []))
                processed_data["research_timestamp"] = datetime.now().isoformat()
                
                return processed_data
            else:
                return {
                    "plant_name": plant_name,
                    "error": "Failed to extract JSON from response",
                    "raw_response": response_text
                }
                
        except Exception as e:
            return {
                "plant_name": plant_name,
                "error": f"Data extraction failed: {str(e)}"
            }
    
    def _process_extracted_data(self, data: dict) -> dict:
        """Process and validate extracted data."""
        
        plant_name = data.get("plant_name", "")
        units_info = data.get("units_info", [])
        
        # Calculate total capacity from units (only units that should be included)
        total_capacity = 0
        operational_statuses = []
        included_units = []
        excluded_units = []
        
        for unit in units_info:
            capacity = unit.get("capacity_mw", 0)
            status = unit.get("operational_status", "").lower()
            include_in_total = unit.get("include_in_total", True)  # Default to True for backward compatibility
            
            # Determine if unit should be included based on status if include_in_total is not explicitly set
            if "include_in_total" not in unit:
                status_lower = status.lower()
                if any(term in status_lower for term in ["announced", "planned", "proposed", "under construction", "future", "planned"]):
                    include_in_total = False
                else:
                    include_in_total = True
            
            if include_in_total and isinstance(capacity, (int, float)):
                total_capacity += capacity
                included_units.append(unit)
            else:
                excluded_units.append(unit)
            
            # Collect operational statuses only from included units for overall status determination
            if include_in_total and status:
                operational_statuses.append(status)
        
        # Determine overall operational status
        overall_status = self._determine_overall_status(operational_statuses)
        
        # Override with provided total if available and reasonable
        provided_total = data.get("total_capacity_mw", 0)
        if isinstance(provided_total, (int, float)) and provided_total > 0:
            total_capacity = provided_total
        
        processed_data = {
            "plant_name": plant_name,
            "total_capacity_mw": int(total_capacity) if total_capacity > 0 else "Not Available",
            "overall_operational_status": overall_status,
            "units_info": units_info,
            "included_units_count": len(included_units),
            "excluded_units_count": len(excluded_units)
        }
        
        # Add summary info for debugging
        if included_units:
            processed_data["included_units_summary"] = [
                f"{unit.get('unit_name', 'Unknown')}: {unit.get('capacity_mw', 0)} MW ({unit.get('operational_status', 'Unknown')})"
                for unit in included_units
            ]
        
        if excluded_units:
            processed_data["excluded_units_summary"] = [
                f"{unit.get('unit_name', 'Unknown')}: {unit.get('capacity_mw', 0)} MW ({unit.get('operational_status', 'Unknown')})"
                for unit in excluded_units
            ]
        
        return processed_data
    
    def _determine_overall_status(self, statuses: list) -> str:
        """Determine overall operational status based on unit statuses."""
        
        if not statuses:
            return "Not Available"
        
        # Normalize statuses
        normalized_statuses = []
        for status in statuses:
            status_lower = status.lower()
            if any(term in status_lower for term in ["operational", "operating", "active", "running"]):
                normalized_statuses.append("operational")
            elif any(term in status_lower for term in ["retired", "shutdown", "closed"]):
                normalized_statuses.append("retired")
            elif any(term in status_lower for term in ["decommissioned", "demolished", "dismantled"]):
                normalized_statuses.append("decommissioned")
            else:
                normalized_statuses.append("unknown")
        
        # Apply logic rules
        if "operational" in normalized_statuses:
            return "Operational"
        elif all(s == "retired" for s in normalized_statuses):
            return "Retired"
        elif all(s == "decommissioned" for s in normalized_statuses):
            return "Decommissioned"
        elif all(s in ["retired", "decommissioned"] for s in normalized_statuses):
            return "Retired"
        else:
            return "Not Available"

def main():
    """Main function to run capacity and status extraction."""
    
    try:
        # Read plant names from CSV
        df = pd.read_csv("/Users/<USER>/Documents/new_powerplant_details/powerplant_names.csv")
        plant_names = df['names'].tolist()
        
        print(f"🚀 Starting power plant capacity and status extraction")
        print(f"Using Gemini Research Agent with web search capabilities")
        print(f"Plants to research: {len(plant_names)}")
        print(f"{'='*60}")
        
        # Initialize extractor
        extractor = CapacityStatusExtractor()
        
        results = []
        for i, plant in enumerate(plant_names):
            print(f"\n[{i+1}/{len(plant_names)}] {plant}")
            print("-" * 50)
            
            try:
                result = extractor.extract_plant_info(plant)
                results.append(result)
                
                # Print key findings
                if "error" not in result:
                    print(f"✅ Total Capacity: {result.get('total_capacity_mw', 'N/A')} MW")
                    print(f"✅ Overall Operational Status: {result.get('overall_operational_status', 'N/A')}")
                    
                    included_count = result.get('included_units_count', 0)
                    excluded_count = result.get('excluded_units_count', 0)
                    total_units = included_count + excluded_count
                    
                    print(f"✅ Units Found: {total_units} (Included: {included_count}, Excluded: {excluded_count})")
                    print(f"✅ Sources: {result.get('research_sources_count', 0)}")
                    
                    # Show included units summary
                    if result.get('included_units_summary'):
                        print("📋 Included Units:")
                        for unit_summary in result.get('included_units_summary', []):
                            print(f"   • {unit_summary}")
                    
                    # Show excluded units summary if any
                    if result.get('excluded_units_summary'):
                        print("🚫 Excluded Units (Future/Announced):")
                        for unit_summary in result.get('excluded_units_summary', []):
                            print(f"   • {unit_summary}")
                else:
                    print(f"❌ Error: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                results.append({"plant_name": plant, "error": str(e)})
                print(f"❌ Error: {e}")
            
            # Add delay between requests (except for the last one)
            if i < len(plant_names) - 1:
                print("⏳ Waiting 5 seconds before next request...")
                time.sleep(5)
        
        # Create output DataFrame
        output_data = []
        for result in results:
            if "error" not in result:
                output_data.append({
                    "Plant Name": result.get("plant_name", ""),
                    "Total Capacity (MW)": result.get("total_capacity_mw", "Not Available"),
                    "Overall Operational Status": result.get("overall_operational_status", "Not Available")
                })
            else:
                output_data.append({
                    "Plant Name": result.get("plant_name", ""),
                    "Total Capacity (MW)": "Error",
                    "Overall Operational Status": "Error"
                })
        
        df_out = pd.DataFrame(output_data)
        
        # Save to Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"plant_capacity_status_{timestamp}.xlsx"
        df_out.to_excel(output_filename, index=False)
        
        print(f"\n{'='*60}")
        print("EXTRACTION COMPLETED")
        print(f"{'='*60}")
        print(f"✅ Results exported to: {output_filename}")
        
        # Print summary
        successful = len([r for r in results if "error" not in r])
        failed = len(results) - successful
        
        print(f"📊 Summary:")
        print(f"   • Successful: {successful}")
        print(f"   • Failed: {failed}")
        print(f"   • Total: {len(results)}")
        
        if successful > 0:
            total_sources = sum(r.get("research_sources_count", 0) for r in results if "error" not in r)
            print(f"   • Total sources found: {total_sources}")
        
        print(f"\n🎉 Capacity and status extraction completed successfully!")
        
    except FileNotFoundError:
        print("❌ Error: 'powerplant_names.csv' not found.")
        print("Creating sample file for demonstration...")
        
        # Create sample data
        sample_data = {'names': ['Great Waste Coal power station']}
        df = pd.DataFrame(sample_data)
        df.to_csv("/Users/<USER>/Documents/new_powerplant_details/powerplant_names.csv", index=False)
        
        print("✅ Sample 'powerplant_names.csv' created. Please run the script again.")
        
    except Exception as e:
        print(f"❌ An error occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()