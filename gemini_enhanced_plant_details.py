#!/usr/bin/env python3
"""
Enhanced Power Plant Details Extractor using Gemini Research Agent
Uses the advanced research agent to gather comprehensive plant information
matching the OpenAI format but with enhanced web search capabilities
"""

import os
import time
import pandas as pd
import json
import re
from datetime import datetime
from gemini_research_agent import GeminiResearchAgent
from dotenv import load_dotenv

load_dotenv()

class EnhancedPlantDetailsExtractor:
    """Extract comprehensive power plant details using Gemini Research Agent."""
    
    def __init__(self):
        self.research_agent = GeminiResearchAgent()
        
    def extract_plant_info(self, plant_name: str) -> dict:
        """
        Extract comprehensive plant information using the research agent.
        
        Args:
            plant_name: Name of the power plant to research
            
        Returns:
            Dictionary containing structured plant information
        """
        
        print(f"🔍 Processing: {plant_name}")
        
        try:
            # Step 1: Comprehensive research
            research_result = self._conduct_comprehensive_research(plant_name)
            
            # Step 2: Extract structured data
            structured_data = self._extract_structured_data(plant_name, research_result)
            
            return structured_data
            
        except Exception as e:
            print(f"❌ Error processing {plant_name}: {e}")
            return {
                "plant_name": plant_name,
                "error": f"Processing failed: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    def _conduct_comprehensive_research(self, plant_name: str) -> dict:
        """Conduct focused research on the power plant for specific fields."""
        
        research_query = f"""
        Research specific information about the power plant: "{plant_name}"
        
        Please find and provide detailed information on the following aspects:

        1. UNIT COMMENCEMENT DATES:
           - What is the commencement date of latest unit of this power plant?
           - If the plant has multiple units, provide commencement date of the most recent unit 
           - Include information commencement date of all units if available to identify the latest one

        2. OPERATIONAL STATUS:
           - Is this power plant currently operational? (Yes/No)
           - What is the current status of the plant (operational, under maintenance, decommissioned, etc.)?
           - Any recent updates on plant operations or shutdowns

        Please prioritize official sources, government databases, company websites, and industry reports for accurate information. Focus specifically on commissioning dates and current operational status.
        """
        
        return self.research_agent.research(research_query)
    
    def _extract_structured_data(self, plant_name: str, research_result: dict) -> dict:
        """Extract structured data from research results."""
        
        research_content = research_result.get("answer", "")
        
        extraction_prompt = f"""
        Based on the research content provided below about "{plant_name}", extract the following information and format it as a JSON object.
        
        Extract these specific fields matching the required format:
        1. latest_unit_commencement_date: What is the latest unit commencement date of this power plant? (YYYY-MM-DD format, or "Not Available")
        2. commencement_year: Extract only the year from the latest unit commencement date (YYYY format, or "Not Available")
        3. operational_status: Is this power plant currently operational? ("Yes" or "No", or "Not Available")
        
        Research Content:
        {research_content}
        
        Respond ONLY with a valid JSON object in this exact format:
        {{
          "plant_name": "{plant_name}",
          "latest_unit_commencement_date": "...",
          "commencement_year": "...",
          "operational_status": "..."
        }}
        
        Extract only information that is explicitly stated in the research content. If any information is not found, use "Not Available".
        For the commencement_year field, if you have a date like "2015-03-15", extract only "2015". If no date is available, use "Not Available".
        For operational_status, use "Yes" if the plant is currently operational, "No" if it's not operational/decommissioned, or "Not Available" if status is unclear.
        """
        
        try:
            # Use research agent to extract structured data
            result = self.research_agent.research(extraction_prompt)
            response_text = result.get("answer", "")
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_text = json_match.group(0)
                structured_data = json.loads(json_text)
                
                # Add metadata
                structured_data["research_sources_count"] = len(research_result.get("sources", []))
                structured_data["research_timestamp"] = datetime.now().isoformat()
                
                return structured_data
            else:
                return {
                    "plant_name": plant_name,
                    "error": "Failed to extract JSON from response",
                    "raw_response": response_text
                }
                
        except Exception as e:
            return {
                "plant_name": plant_name,
                "error": f"Data extraction failed: {str(e)}"
            }

def main():
    """Main function to run enhanced plant research."""
    
    try:
        # Read plant names from CSV
        df = pd.read_csv("powerplant_names.csv")
        plant_names = df['names'].tolist()
        
        print(f"🚀 Starting enhanced power plant research")
        print(f"Using Gemini Research Agent with web search capabilities")
        print(f"Plants to research: {plant_names}")
        print(f"{'='*60}")
        
        # Initialize extractor
        extractor = EnhancedPlantDetailsExtractor()
        
        results = []
        for i, plant in enumerate(plant_names):
            print(f"\n[{i+1}/{len(plant_names)}] {plant}")
            print("-" * 50)
            
            try:
                result = extractor.extract_plant_info(plant)
                results.append(result)
                
                # Print key findings
                if "error" not in result:
                    print(f"✅ Latest Unit Commencement Date: {result.get('latest_unit_commencement_date', 'N/A')}")
                    print(f"✅ Commencement Year: {result.get('commencement_year', 'N/A')}")
                    print(f"✅ Operational Status: {result.get('operational_status', 'N/A')}")
                    print(f"✅ Sources: {result.get('research_sources_count', 0)}")
                else:
                    print(f"❌ Error: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                results.append({"plant_name": plant, "error": str(e)})
                print(f"❌ Error: {e}")
            
            # Add delay between requests (except for the last one)
            if i < len(plant_names) - 1:
                print("⏳ Waiting 5 seconds before next request...")
                time.sleep(5)
        
        # Create output DataFrame with only required fields
        df_out = pd.DataFrame(results)
        
        # Filter to only include required columns
        required_columns = [
            "plant_name",
            "latest_unit_commencement_date",
            "commencement_year",
            "operational_status"
        ]
        
        # Rename columns to match user requirements
        column_mapping = {
            "plant_name": "Plant Name",
            "latest_unit_commencement_date": "Latest Unit Commencement Date",
            "commencement_year": "Commencement Year",
            "operational_status": "Operational Status"
        }
        
        # Select only required columns that exist in the dataframe
        available_columns = [col for col in required_columns if col in df_out.columns]
        df_filtered = df_out[available_columns].copy()
        
        # Rename columns
        df_filtered = df_filtered.rename(columns=column_mapping)
        
        # Save to Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"enhanced_plant_info_gemini_{timestamp}.xlsx"
        df_filtered.to_excel(output_filename, index=False)
        
        print(f"\n{'='*60}")
        print("RESEARCH COMPLETED")
        print(f"{'='*60}")
        print(f"✅ Results exported to: {output_filename}")
        
        # Print summary
        successful = len([r for r in results if "error" not in r])
        failed = len(results) - successful
        
        print(f"📊 Summary:")
        print(f"   • Successful: {successful}")
        print(f"   • Failed: {failed}")
        print(f"   • Total: {len(results)}")
        
        if successful > 0:
            total_sources = sum(r.get("research_sources_count", 0) for r in results if "error" not in r)
            print(f"   • Total sources found: {total_sources}")
        
        print(f"\n🎉 Enhanced research completed successfully!")
        
    except FileNotFoundError:
        print("❌ Error: 'plant_names.csv' not found.")
        print("Creating sample file for demonstration...")
        
        # Create sample data
        sample_data = {'names': ['Great Waste Coal power station']}
        df = pd.DataFrame(sample_data)
        df.to_csv("powerplant_names.csv", index=False)
        
        print("✅ Sample 'plant_names.csv' created. Please run the script again.")
        
    except Exception as e:
        print(f"❌ An error occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()